<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\MediaLibrary\Conversions\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Aliziodev\LaravelTaxonomy\Traits\HasTaxonomy;

class User extends Authenticatable implements MustVerifyEmail, HasMedia
{
    use HasApiTokens, HasFactory, Notifiable, InteractsWithMedia, HasTaxonomy;

    protected array $guard_name = ['sanctum', 'web'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'email_verified_at',
        'role',
        'phone',
        'post_code',
        'city',
        'country',
        'photo',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    #protected $with = ['accountStatus'];

    /**
     * Local scope to exclude auth user
     * @param $query
     * @return mixed
     */
    public function scopeWithoutAuthUser($query): mixed
    {
        return $query->where('id', '!=', auth()->id());
    }

    /**
     * Local scope to exclude super admin
     * @param $query
     * @return mixed
     */
    public function scopeWithoutSuperAdmin($query): mixed
    {
        return $query->where('id', '!=', 1);
    }

    /**
     * Get the user's subscriptions
     */
    public function subscriptions()
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get the user's active subscription
     */
    public function activeSubscription()
    {
        return $this->hasOne(UserSubscription::class)->where('status', 'active');
    }

    /**
     * Get the user's active subscription with product loaded
     */
    public function activeSubscriptionWithProduct()
    {
        return $this->hasOne(UserSubscription::class)
            ->where('status', 'active')
            ->with('subscriptionProduct');
    }

    /**
     * Get the user's payments
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the user's invoices
     */
    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get the user's refunds
     */
    public function refunds()
    {
        return $this->hasMany(Refund::class);
    }

    /**
     * Get the user's payment methods
     */
    public function paymentMethods()
    {
        return $this->hasMany(PaymentMethod::class);
    }

    /**
     * Check if user has a specific role
     */
    public function hasRole(string $role, string $guard = null): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if user has any of the given roles
     */
    public function hasAnyRole(array $roles, string $guard = null): bool
    {
        return in_array($this->role, $roles);
    }

    /**
     * Check if user is admin or super-admin
     */
    public function isAdmin(): bool
    {
        return in_array($this->role, ['admin', 'super-admin']);
    }

    /**
     * Check if user is super-admin
     */
    public function isSuperAdmin(): bool
    {
        return $this->role === 'super-admin';
    }





    /**
     * Get the user's default payment method
     */
    public function defaultPaymentMethod()
    {
        return $this->hasOne(PaymentMethod::class)->where('is_default', true);
    }

    /**
     * Check if user has an active subscription
     */
    public function hasActiveSubscription(): bool
    {
        return $this->activeSubscription()->exists();
    }

    /**
     * Check if user account is locked
     */
    public function isAccountLocked(): bool
    {
        $accountStatus = $this->accountStatus;
        return $accountStatus && is_object($accountStatus) ? $accountStatus->is_locked : false;
    }

    /**
     * Lock user account (updated to use normalized account status)
     */
    public function lockAccount(string $reason = 'Account locked', ?int $lockedBy = null): void
    {
        if (!$this->accountStatus) {
            $this->accountStatus()->create(['user_id' => $this->id]);
            $this->load('accountStatus');
        }

        $this->accountStatus->lock($reason, $lockedBy);
    }

    /**
     * Unlock user account (updated to use normalized account status)
     */
    public function unlockAccount(?int $unlockedBy = null): void
    {
        if ($this->accountStatus) {
            $this->accountStatus->unlock($unlockedBy);
        }
    }

    public function getPendingEmail()
    {
        return null;
    }

    /**
     * Get the user's investor profile
     */
    public function investorProfile()
    {
        return $this->hasOne(InvestorProfile::class);
    }

    /**
     * Get the user's startup profile
     */
    public function startupProfile()
    {
        return $this->hasOne(StartupProfile::class);
    }

    /**
     * Get account status for this user
     */
    public function
    accountStatus(): HasOne
    {
        return $this->hasOne(UserAccountStatus::class);
    }

    /**
     * Get social media links for this user
     */
    public function socialMediaLinks(): MorphMany
    {
        return $this->morphMany(SocialMediaLink::class, 'linkable');
    }

    /**
     * Get categories associated with this user
     * For startup users, delegate to their startup profile
     * For other users, use direct taxonomy relationship
     */
    public function categories()
    {
        if ($this->role === 'startup' && $this->startupProfile) {
            // Return the startup profile's categories relationship
            return $this->startupProfile->taxonomies()->where('type', 'category');
        }

        // For non-startup users, return their direct taxonomy relationship
        return $this->taxonomies()->where('type', 'category');
    }

    /**
     * Get interest requests sent by this user
     */
    public function sentInterestRequests()
    {
        return $this->hasMany(InterestRequest::class, 'requester_id');
    }

    /**
     * Get interest requests received by this user
     */
    public function receivedInterestRequests()
    {
        return $this->hasMany(InterestRequest::class, 'target_id');
    }

    /**
     * Get interest requests approved by this user (for analysts)
     */
    public function approvedInterestRequests()
    {
        return $this->hasMany(InterestRequest::class, 'approved_by');
    }

    /**
     * Get all interest requests related to this user (sent or received)
     * Note: This returns a query builder, not a relationship
     */
    public function interestRequests()
    {
        return InterestRequest::where('requester_id', $this->id)
            ->orWhere('target_id', $this->id);
    }

    /**
     * Get all interest requests related to this user as a collection
     */
    public function getAllInterestRequests()
    {
        return InterestRequest::where('requester_id', $this->id)
            ->orWhere('target_id', $this->id)
            ->with(['requester', 'target', 'approver'])
            ->get();
    }

    /**
     * Check if user is an investor
     */
    public function isInvestor(): bool
    {
        return $this->role === 'investor';
    }

    /**
     * Check if user is a startup
     */
    public function isStartup(): bool
    {
        return $this->role === 'startup';
    }

    /**
     * Check if user is an analyst
     */
    public function isAnalyst(): bool
    {
        return $this->role === 'analyst';
    }

    /**
     * Check if user has completed their profile
     */
    public function hasCompletedProfile(): bool
    {
        if ($this->isInvestor()) {
            return $this->investorProfile && $this->investorProfile->profile_completed;
        }

        if ($this->isStartup()) {
            return $this->startupProfile && $this->startupProfile->profile_completed;
        }

        return true; // Analysts don't need profile completion
    }

    /**
     * Get user's role type for investment platform
     */
    public function getInvestmentRoleType(): ?string
    {
        if ($this->isInvestor()) return 'investor';
        if ($this->isStartup()) return 'startup';
        if ($this->isAnalyst()) return 'analyst';

        return null;
    }

    // Backward compatibility accessors for account status fields

    /**
     * Get stripe customer ID (backward compatibility)
     */
    public function getStripeCustomerIdAttribute(): ?string
    {
        $accountStatus = $this->accountStatus;
        return $accountStatus && is_object($accountStatus) ? $accountStatus->stripe_customer_id : null;
    }

    /**
     * Get account locked status (backward compatibility)
     */
    public function getAccountLockedAttribute(): bool
    {
        $accountStatus = $this->accountStatus;
        return $accountStatus && is_object($accountStatus) ? $accountStatus->is_locked : false;
    }

    /**
     * Get account locked at timestamp (backward compatibility)
     */
    public function getAccountLockedAtAttribute(): ?string
    {
        $accountStatus = $this->accountStatus;
        return $accountStatus && is_object($accountStatus) ? $accountStatus->locked_at : null;
    }

    /**
     * Get account status string (backward compatibility)
     */
    public function getAccountStatusStringAttribute(): string
    {
        $accountStatus = $this->accountStatus;
        return $accountStatus && is_object($accountStatus) ? $accountStatus->status : 'active';
    }

    /**
     * Check if account is accessible (not locked or suspended)
     */
    public function isAccountAccessible(): bool
    {
        $accountStatus = $this->accountStatus;
        return $accountStatus && is_object($accountStatus) ? $accountStatus->isAccessible() : true;
    }


}
